#!/usr/bin/env python3

import json
import random
import numpy as np
import os
import networkx as nx
from collections import defaultdict
import matplotlib.pyplot as plt
from tqdm import tqdm
import time
import logging
import argparse
import abc
from clos_topo import Clos


class TrafficPattern(abc.ABC):
    """流量模式的抽象基类 (策略接口)"""
    
    def __init__(self, config):
        self.config = config

    @abc.abstractmethod
    def generate_flows(self, batch_id, path_manager, flow_counter_start=0):
        """生成一个批次的流数据"""
        pass

    @staticmethod
    def create_flow_dict(flow_id, source, target, path, flow_size, start_time, workload, is_congested, is_burst, **kwargs):
        """通用的流创建辅助函数"""
        flow_data = {
            "flow_id": flow_id,
            "source": source,
            "target": target,
            "path": path,
            "flow_features": [flow_size],
            "start_time": start_time,
            "workload": {
                "model": workload["model"],
                "dataset": workload["dataset"],
                "parameters": workload.get("params", 0)
            },
            "is_congested": is_congested,
            "is_burst": is_burst
        }
        flow_data.update(kwargs)  # 用于添加 wave_index 等额外信息
        return flow_data


class NormalTraffic(TrafficPattern):
    """普通流量模式 (具体策略)"""
    
    def generate_flows(self, batch_id, path_manager, flow_counter_start=0):
        """生成普通模式的流数据"""
        flows = []
        flow_counter = flow_counter_start
        
        # 随机化批次参数
        num_flows = random.randint(*self.config['flows_per_batch_range'])
        
        # 选择目标Pod
        path_manager.select_target_pods(self.config.get('target_pods_count', 2))
        
        print(f"生成普通批次: {num_flows} 个流")
        
        # 生成流（不再区分拥塞流和正常流）
        successful_flows = 0
        for i in range(num_flows):
            source, target = path_manager.generate_normal_flow_pair()
            if source and target:
                path = path_manager.get_cross_pod_path(source, target)
                if path and len(path) == 5:  # 确保是4跳路径
                    flow = self._create_flow(f"{batch_id}_norm_flow_{flow_counter}", 
                                           source, target, path)
                    flows.append(flow)
                    successful_flows += 1
                    flow_counter += 1
        
        # 按照开始时间排序
        flows.sort(key=lambda x: x['start_time'])
        
        print(f"普通批次生成完成: 总共 {len(flows)} 个流")
        print(f"  -> 成功生成流: {successful_flows}/{num_flows}")
        
        return {
            "batch_id": batch_id,
            "flows": flows,
            "metadata": {
                "num_flows": len(flows),
                "is_burst": False,
                "target_pods": path_manager.target_pods,
            },
            "next_flow_counter": flow_counter
        }

    def _create_flow(self, flow_id, source, target, path):
        """创建单个流的数据结构"""
        flow_size = random.uniform(*self.config['flow_size_range'])
        start_time = random.uniform(*self.config['flow_arrival_interval_range'])
        workload = random.choice(DL_WORKLOADS)
        
        return TrafficPattern.create_flow_dict(
            flow_id, source, target, path, flow_size, start_time,
            workload, False, is_burst=False
        )


class BurstTraffic(TrafficPattern):
    """多波次爆发流量模式 (具体策略)"""
    
    def generate_flows(self, batch_id, path_manager, flow_counter_start=0):
        """生成爆发模式的流数据"""
        flows = []
        flow_counter = flow_counter_start
        
        # 选择目标Pod
        path_manager.select_target_pods(self.config['target_pods_count'])
        
        # 计算爆发参数
        num_flows = random.randint(*self.config['flow_count_range'])
        waves_count = random.randint(*self.config['waves_count_range'])
        
        # 计算波次分布和时间
        wave_flows_distribution = self._calculate_wave_distribution(num_flows, waves_count)
        wave_start_times = self._calculate_wave_start_times(waves_count)
        
        print(f"生成多波次爆发批次: {waves_count} 波，总流量 {num_flows} 个")
        
        total_flows_generated = 0
        
        # 生成每个波次的流
        for wave_idx in range(waves_count):
            wave_flows = wave_flows_distribution[wave_idx]
            wave_start_time = wave_start_times[wave_idx]
            
            print(f"  第 {wave_idx + 1} 波 (时间 {wave_start_time:.2f}s): {wave_flows} 个流")
            
            # 生成该波次的流（不再区分拥塞流和正常流）
            for i in range(wave_flows):
                flow_start_time = wave_start_time + random.uniform(*self.config['time_window'])
                flow_size = self._get_flow_size_by_ratio(i, wave_flows)
                
                source, target = path_manager.generate_normal_flow_pair()
                if source and target:
                    path = path_manager.get_cross_pod_path(source, target)
                    if path and len(path) == 5:  # 确保是4跳路径
                        flow = self._create_burst_flow(
                            f"{batch_id}_wave{wave_idx}_flow_{flow_counter}",
                            source, target, path, flow_size, flow_start_time,
                            is_burst=True, wave_index=wave_idx
                        )
                        flows.append(flow)
                        total_flows_generated += 1
                        flow_counter += 1
        
        # 按照开始时间排序
        flows.sort(key=lambda x: x['start_time'])
        
        print(f"多波次爆发批次生成完成: 总共 {len(flows)} 个流")
        print(f"  -> 总生成流: {total_flows_generated}")
        print(f"  -> 波次分布: {wave_flows_distribution}")
        
        return {
            "batch_id": batch_id,
            "flows": flows,
            "metadata": {
                "num_flows": len(flows),
                "is_burst": True,
                "waves_count": waves_count,
                "wave_distribution": wave_flows_distribution,
                "wave_start_times": wave_start_times,
                "target_pods": path_manager.target_pods,
                "generation_success_rate": len(flows) / num_flows if num_flows > 0 else 0
            },
            "next_flow_counter": flow_counter
        }

    def _calculate_wave_distribution(self, total_flows, waves_count):
        """计算每个波次的流量分配"""
        weights = [self.config['intensity_decay'] ** i for i in range(waves_count)]
        total_weight = sum(weights)
        
        wave_flows = []
        allocated_flows = 0
        
        for i in range(waves_count - 1):
            flows_for_wave = int(total_flows * weights[i] / total_weight)
            wave_flows.append(flows_for_wave)
            allocated_flows += flows_for_wave
        
        # 最后一波分配剩余的流量
        wave_flows.append(total_flows - allocated_flows)
        return wave_flows

    def _calculate_wave_start_times(self, waves_count):
        """计算每个波次的开始时间"""
        start_times = [0.0]  # 第一波从时间0开始
        
        current_time = 0.0
        for i in range(1, waves_count):
            interval = random.uniform(*self.config['wave_interval_range'])
            current_time += interval
            start_times.append(current_time)
        
        return start_times

    def _get_flow_size_by_ratio(self, flow_index, total_flows):
        """根据比例和索引确定流大小"""
        large_ratio = self.config['large_flow_ratio']
        medium_ratio = self.config['medium_flow_ratio']
        
        if flow_index < total_flows * large_ratio:
            return random.uniform(300, 500)  # 大流
        elif flow_index < total_flows * (large_ratio + medium_ratio):
            return random.uniform(100, 300)  # 中等流
        else:
            return random.uniform(50, 100)   # 小流

    def _create_burst_flow(self, flow_id, source, target, path, flow_size, start_time,
                          is_burst, wave_index):
        """创建爆发模式的流数据结构"""
        workload = random.choice(DL_WORKLOADS)
        
        return TrafficPattern.create_flow_dict(
            flow_id, source, target, path, flow_size, start_time,
            workload, False, is_burst, wave_index=wave_index
        )


class TimeClusterManager:
    """时间聚集管理器 - 处理流的时间点聚集"""
    
    def __init__(self, config):
        self.config = config
        self.clustering_enabled = config.get('enabled', True)
        self.clustering_ratio = config.get('clustering_ratio', 0.3)
        self.flows_per_cluster_range = config.get('flows_per_cluster_range', (2, 3))
        self.min_cluster_interval = config.get('min_cluster_interval', 0.1)
        self.path_diversity_check = config.get('path_diversity_check', True)
    
    def apply_time_clustering(self, flows, path_manager):
        """对流应用时间聚集"""
        if not self.clustering_enabled or len(flows) < 4:
            return flows
        
        print(f"  -> 应用时间聚集 (聚集比例: {self.clustering_ratio:.1%})")
        
        # 1. 选择聚集点
        cluster_timepoints = self._select_cluster_timepoints(flows)
        if not cluster_timepoints:
            return flows
        
        # 2. 将流分配到聚集点
        clustered_flows = self._cluster_flows_by_time(flows, cluster_timepoints)
        
        # 3. 确保路径多样性
        if self.path_diversity_check:
            clustered_flows = self._ensure_path_diversity(clustered_flows, path_manager)
        
        # 4. 统计聚集效果
        self._log_clustering_stats(clustered_flows, cluster_timepoints)
        
        return clustered_flows
    
    def _select_cluster_timepoints(self, flows):
        """选择聚集时间点"""
        # 获取所有流的开始时间
        start_times = [flow['start_time'] for flow in flows]
        start_times.sort()
        
        # 计算需要的聚集点数量
        target_clustered_flows = int(len(flows) * self.clustering_ratio)
        avg_flows_per_cluster = sum(self.flows_per_cluster_range) / 2
        num_clusters = max(1, int(target_clustered_flows / avg_flows_per_cluster))
        
        # 选择聚集时间点，确保间隔足够
        cluster_points = []
        time_range = max(start_times) - min(start_times)
        
        if time_range < self.min_cluster_interval * num_clusters:
            # 时间范围太小，只选择一个聚集点
            cluster_points = [random.choice(start_times)]
        else:
            # 从时间范围内选择分散的聚集点
            candidates = [t for t in start_times if 
                         all(abs(t - cp) >= self.min_cluster_interval for cp in cluster_points)]
            
            while len(cluster_points) < num_clusters and candidates:
                new_point = random.choice(candidates)
                cluster_points.append(new_point)
                # 移除太近的候选点
                candidates = [t for t in candidates if abs(t - new_point) >= self.min_cluster_interval]
        
        return cluster_points
    
    def _cluster_flows_by_time(self, flows, cluster_timepoints):
        """将流分配到聚集点"""
        flows_copy = flows.copy()
        
        for cluster_time in cluster_timepoints:
            # 找到距离聚集点最近的流
            nearby_flows = []
            for flow in flows_copy:
                time_diff = abs(flow['start_time'] - cluster_time)
                nearby_flows.append((time_diff, flow))
            
            # 按距离排序
            nearby_flows.sort(key=lambda x: x[0])
            
            # 选择要聚集的流数量
            flows_to_cluster = random.randint(*self.flows_per_cluster_range)
            flows_to_cluster = min(flows_to_cluster, len(nearby_flows))
            
            # 将选中的流时间对齐到聚集点
            for i in range(flows_to_cluster):
                flow = nearby_flows[i][1]
                flow['start_time'] = cluster_time
                flow['is_clustered'] = True
                flow['cluster_time'] = cluster_time
        
        return flows_copy
    
    def _ensure_path_diversity(self, flows, path_manager):
        """确保同一时间点的流有不同路径，并统一工作负载特征"""
        # 按时间点分组
        time_groups = defaultdict(list)
        for flow in flows:
            if flow.get('is_clustered', False):
                time_groups[flow['cluster_time']].append(flow)
        
        # 检查每个时间点的路径多样性和工作负载统一性
        for cluster_time, grouped_flows in time_groups.items():
            if len(grouped_flows) <= 1:
                continue
            
            # 1. 统一工作负载特征
            self._unify_workload_features(grouped_flows)
            
            # 2. 检查路径冲突
            paths = [tuple(flow.get('path', [])) for flow in grouped_flows]
            sources = [flow.get('source') for flow in grouped_flows]
            targets = [flow.get('target') for flow in grouped_flows]
            
            # 如果有重复的源-目标对，重新生成路径
            source_target_pairs = [(s, t) for s, t in zip(sources, targets)]
            if len(set(source_target_pairs)) != len(source_target_pairs):
                print(f"    检测到聚集点 {cluster_time:.2f}s 的路径冲突，重新生成路径")
                self._regenerate_diverse_paths(grouped_flows, path_manager)
        
        return flows
    
    def _unify_workload_features(self, flows):
        """统一同一聚集点的流的工作负载特征"""
        if len(flows) <= 1:
            return
        
        # 使用第一个流的特征作为基准
        reference_flow = flows[0]
        reference_workload = reference_flow.get('workload', {})
        reference_flow_features = reference_flow.get('flow_features', [])
        
        print(f"    统一聚集点工作负载: {reference_workload.get('model', 'Unknown')} - {reference_workload.get('dataset', 'Unknown')}")
        print(f"    统一流特征: {reference_flow_features}")
        
        # 将所有其他流的特征统一为参考流的特征
        for flow in flows[1:]:
            flow['workload'] = reference_workload.copy()
            flow['flow_features'] = reference_flow_features.copy()
    
    def _regenerate_diverse_paths(self, flows, path_manager):
        """为冲突的流重新生成多样化路径"""
        used_pairs = set()
        
        for flow in flows:
            attempts = 0
            max_attempts = 10
            
            while attempts < max_attempts:
                # 重新生成源-目标对
                if flow.get('is_congested', False):
                    source, target = path_manager.generate_congestion_flow_pair()
                else:
                    source, target = path_manager.generate_normal_flow_pair()
                
                pair = (source, target)
                if pair not in used_pairs and source and target:
                    # 获取新路径
                    new_path = path_manager.get_cross_pod_path(source, target)
                    if new_path and len(new_path) == 5:
                        flow['source'] = source
                        flow['target'] = target
                        flow['path'] = new_path
                        used_pairs.add(pair)
                        break
                
                attempts += 1
            
            if attempts >= max_attempts:
                print(f"    警告: 无法为流 {flow.get('flow_id', 'unknown')} 生成唯一路径")
    
    def _log_clustering_stats(self, flows, cluster_timepoints):
        """记录聚集统计信息"""
        clustered_flows = [f for f in flows if f.get('is_clustered', False)]
        
        print(f"    聚集统计: {len(clustered_flows)} 个流聚集到 {len(cluster_timepoints)} 个时间点")
        
        # 按聚集点统计
        cluster_stats = defaultdict(int)
        for flow in clustered_flows:
            cluster_stats[flow['cluster_time']] += 1
        
        for cluster_time, count in cluster_stats.items():
            print(f"      时间点 {cluster_time:.2f}s: {count} 个流")


class DataGenConfig:
    """数据生成配置类 - 异步流模式 + 时间聚集"""
    
    def __init__(self):
        # === 异步流数据生成参数 ===
        self.params = {
            # 批次数量配置
            "num_batches": 100,                      # 训练批次数量
            "num_validation_batches": 20,           # 验证批次数量
            "num_test_batches": 1,                 # 测试批次数量
            
            # 全局配置
            "output_base_dir": "datasets",           # 最终输出基础目录名 (用于IID合规)
            "ensure_iid": True,                      # 确保IID合规性
            
            # === 时间聚集配置 ===
            "time_clustering": {
                "enabled": True,                     # 启用时间聚集
                "clustering_ratio": 0.7,             # 30%的流参与聚集
                "flows_per_cluster_range": (2, 3),   # 每个聚集点2-3条流
                "min_cluster_interval": 0.1,         # 聚集点最小间隔0.1秒
                "path_diversity_check": True         # 启用路径多样性检查
            },
            
            # --- 流量模式配置 (策略模式核心) ---
            "traffic_patterns": {
                "burst_ratio": 0.3,                 # 30%的批次为爆发模式
                "patterns": {
                    "normal": {
                        "flows_per_batch_range": (30, 35),
                        "flow_arrival_interval_range": (0.0, 2.0),
                        "flow_size_range": (50, 500),
                        "congestion_ratio_range": (0.8, 1),
                        "target_pods_count": 10,
                    },
                    "burst": {
                        "flow_count_range": (5, 10),
                        "time_window": (0.0, 0.5),
                        "waves_count_range": (1, 2),
                        "wave_interval_range": (1, 2),
                        "intensity_decay": 0.7,
                        "target_pods_count": 10,
                        "congestion_flow_ratio": 0.3,
                        "large_flow_ratio": 0.4,
                        "medium_flow_ratio": 0.4,
                        "small_flow_ratio": 0.2,
                    }
                }
            }
        }
    
    def update_params(self, **kwargs):
        """更新参数"""
        self.params.update(kwargs)
    
    def print_config(self):
        """打印当前配置"""
        print(f"\n=== 数据生成配置 ===")
        for key, value in self.params.items():
            print(f"{key}: {value}")
        print("=" * 40)

# 全局配置实例
config = DataGenConfig()

# 定义深度学习工作负载
DL_WORKLOADS = [
    {
        "model": "ResNet-50",
        "dataset": "ImageNet",
        "params": 25.6e6,
        "compute_intensity": "中",
        "comm_intensity": "中"
    },
    {
        "model": "BERT-Large",
        "dataset": "SQuAD",
        "params": 340e6,
        "compute_intensity": "高",
        "comm_intensity": "高"
    },
    {
        "model": "DLRM",
        "dataset": "Criteo",
        "params": 130e6,
        "compute_intensity": "中",
        "comm_intensity": "中高"
    },
    {
        "model": "VGG-16",
        "dataset": "ImageNet",
        "params": 138e6,
        "compute_intensity": "中",
        "comm_intensity": "高"
    },
    {
        "model": "DeepSpeech2",
        "dataset": "LibriSpeech",
        "params": 120e6,
        "compute_intensity": "中",
        "comm_intensity": "中"
    }
]





class PathManager:
    """管理路径选择 - 简化版，基于Pod结构"""

    def __init__(self, clos):
        self.clos = clos
        self.target_pods = []  # 目标Pod列表，用于制造拥塞
        self.pod_hosts = self._group_hosts_by_pod()
        
    def _group_hosts_by_pod(self):
        """将主机按Pod分组"""
        pod_hosts = {}
        hosts = self.clos.nodes['H']
        for i, host in enumerate(hosts):
            pod_id = i // 4  # 每个Pod有4个主机
            if pod_id not in pod_hosts:
                pod_hosts[pod_id] = []
            pod_hosts[pod_id].append(host)
        
        print(f"网络包含 {len(pod_hosts)} 个Pod，每个Pod有4个主机")
        return pod_hosts
    
    def select_target_pods(self, num_target_pods=2):
        """选择目标Pod用于制造拥塞"""
        available_pods = list(self.pod_hosts.keys())
        self.target_pods = random.sample(available_pods, min(num_target_pods, len(available_pods)))
        
        print(f"已选择目标Pod: {self.target_pods}")
        for pod_id in self.target_pods:
            print(f"  Pod {pod_id}: {self.pod_hosts[pod_id]}")
        
        return self.target_pods
    
    def get_cross_pod_path(self, source, target):
        """生成跨Pod的4跳路径"""
        try:
            # 在Clos网络中，跨Pod通信自然就是4跳
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=5))
            four_hop_paths = [p for p in all_paths if len(p) == 5]  # 4跳=5个节点
            
            if four_hop_paths:
                return random.choice(four_hop_paths)
            else:
                return None
        except:
            return None
    
    def get_intra_pod_path(self, source, target):
        """生成Pod内的2跳路径"""
        try:
            all_paths = list(self.clos.find_all_paths(source, target, cutoff=3))
            two_hop_paths = [p for p in all_paths if len(p) == 3]  # 2跳=3个节点
            
            if two_hop_paths:
                return random.choice(two_hop_paths)
            else:
                return None
        except:
            return None
    
    def generate_congestion_flow_pair(self):
        """生成指向目标Pod的拥塞流源目标对"""
        if not self.target_pods:
            return None, None
            
        # 选择目标Pod
        target_pod = random.choice(self.target_pods)
        target_host = random.choice(self.pod_hosts[target_pod])
        
        # 选择源主机（来自其他Pod）
        source_pod_candidates = [pod_id for pod_id in self.pod_hosts.keys() if pod_id != target_pod]
        if not source_pod_candidates:
            return None, None
            
        source_pod = random.choice(source_pod_candidates)
        source_host = random.choice(self.pod_hosts[source_pod])
        
        return source_host, target_host
    
    def generate_normal_flow_pair(self):
        """生成正常流的源目标对（避开目标Pod）"""
        non_target_pods = [pod_id for pod_id in self.pod_hosts.keys() if pod_id not in self.target_pods]
        
        if len(non_target_pods) < 2:
            # 如果非目标Pod太少，随机选择任意两个主机
            all_hosts = []
            for hosts in self.pod_hosts.values():
                all_hosts.extend(hosts)
            
            if len(all_hosts) < 2:
                return None, None
            
            selected = random.sample(all_hosts, 2)
            return selected[0], selected[1]
        
        # 从非目标Pod中选择源和目标
        selected_pods = random.sample(non_target_pods, 2)
        source_host = random.choice(self.pod_hosts[selected_pods[0]])
        target_host = random.choice(self.pod_hosts[selected_pods[1]])
        
        return source_host, target_host


class TimeBasedFlowScheduler:
    """基于时间的流调度器 - 流水线模型版本
    采用流水线(Pipelining)/直通交换(Cut-through)模型进行仿真。
    一个流的速率由其路径上的瓶颈链路决定。
    """

    def __init__(self, topology, tasks_data=None, timestep=0.01, max_time=10.0):
        self.topology = topology
        self.timestep = timestep
        self.max_time = max_time
        self.current_time = 0.0
        
        # 任务和流状态管理
        self.tasks_data = tasks_data if tasks_data else []
        self.pending_flows = {}   # 等待开始的流 {start_time: [flow_info, ...]}
        self.active_flows = {}    # 正在传输的流 {flow_id: flow_info}
        self.completed_flows = {} # 已完成的流 {flow_id: flow_info}

        # 性能优化：增量维护边竞争计数
        self.edge_contention = defaultdict(int)

        # 仿真结果
        self.flow_completion_times = {} # 记录每个流的完成时间 {flow_id: completion_time}

        # 初始化任务
        if self.tasks_data:
            self.initialize_flows()

    def initialize_flows(self):
        """
        初始化所有流，将它们放入等待队列。
        """
        for task in self.tasks_data:
            flow_id = task['task_id']
            start_time = round(task.get('comm_start_time', 0.0), 4) # 对开始时间进行取整以分组
            
            flow_info = {
                'flow_id': flow_id,
                'path': task['paths'][0],
                'size': task['gradient_size_mb'],
                'start_time': start_time,
                'total_bytes_sent': 0.0,
                'end_time': -1.0
            }

            if start_time not in self.pending_flows:
                self.pending_flows[start_time] = []
            
            # 将流信息添加到对应开始时间的列表中
            self.pending_flows[start_time].append(flow_info)
        
        # 打印初始化信息
        total_flows = len(self.tasks_data)
        print(f"调度器初始化完成: {total_flows} 个流被加载到等待队列。")

    def _add_flow_to_contention(self, flow_info):
        """当一个流开始时，增加其路径上所有边的计数值"""
        path = flow_info['path']
        for i in range(len(path) - 1):
            edge = (path[i], path[i+1])
            self.edge_contention[edge] += 1

    def _remove_flow_from_contention(self, flow_info):
        """当一个流结束时，减少其路径上所有边的计数值"""
        path = flow_info['path']
        for i in range(len(path) - 1):
            edge = (path[i], path[i+1])
            self.edge_contention[edge] -= 1
            if self.edge_contention[edge] == 0:
                del self.edge_contention[edge] # 保持字典干净

    def simulate_time_based(self):
        """
        运行基于时间的流水线模型仿真。
        """
        print(f"开始流水线模型仿真，最大时间: {self.max_time}s，时间步长: {self.timestep}s")
        
        # 按时间排序等待队列的键
        pending_times = sorted(self.pending_flows.keys())
        
        from tqdm import tqdm
        pbar = tqdm(total=len(self.tasks_data), desc="Simulating Flows")

        while self.current_time < self.max_time:
            
            # 1. 启动新到达的流
            # 检查是否有在当前时间步或之前需要开始的流
            while pending_times and self.current_time >= pending_times[0]:
                start_time = pending_times.pop(0)
                for flow_info in self.pending_flows[start_time]:
                    self.active_flows[flow_info['flow_id']] = flow_info
                    self._add_flow_to_contention(flow_info)  # 增量更新
                    # print(f"时间 {self.current_time:.3f}s: 流 {flow_info['flow_id']} 开始传输。")

            # 如果没有活动流，直接跳到下一个有流开始的时间点，以加速仿真
            if not self.active_flows:
                if not pending_times:
                    # 所有流都完成了
                    print("所有流均已完成或启动，仿真结束。")
                    break
                else:
                    self.current_time = pending_times[0]
                    continue # 跳到下一个循环以启动新流

            # 2. 计算每个活跃流的瓶颈带宽
            flow_rates = {}
            for flow_id, flow_info in self.active_flows.items():
                min_bw = float('inf')
                path = flow_info['path']
                for i in range(len(path) - 1):
                    edge = (path[i], path[i+1])
                    try:
                        capacity = self.topology.G[edge[0]][edge[1]]['btw']
                    except KeyError:
                        capacity = 4000 # 默认带宽

                    # 从预维护的计数中获取竞争数 - O(1)操作
                    contention = self.edge_contention[edge]
                    
                    # 公平分享带宽
                    shared_bw = capacity / contention if contention > 0 else capacity
                    if shared_bw < min_bw:
                        min_bw = shared_bw
                
                flow_rates[flow_id] = min_bw

            # 3. 根据瓶颈速率更新流的传输进度
            flows_to_complete = []
            for flow_id, flow_info in self.active_flows.items():
                rate = flow_rates[flow_id]
                
                # 在这个时间步内传输的数据量
                bytes_sent = rate * self.timestep
                flow_info['total_bytes_sent'] += bytes_sent
                
                # 检查流是否完成
                if flow_info['total_bytes_sent'] >= flow_info['size']:
                    flows_to_complete.append(flow_id)

            # 4. 处理已完成的流
            if flows_to_complete:
                for flow_id in flows_to_complete:
                    if flow_id in self.active_flows: # 确保流还在活跃列表中
                        completed_flow = self.active_flows.pop(flow_id)
                        
                        # 计算精确的完成时间
                        overshoot_bytes = completed_flow['total_bytes_sent'] - completed_flow['size']
                        overshoot_time = overshoot_bytes / flow_rates[flow_id] if flow_rates[flow_id] > 0 else 0
                        completion_time = self.current_time + self.timestep - overshoot_time
                        
                        completed_flow['end_time'] = completion_time
                        self.completed_flows[flow_id] = completed_flow
                        self.flow_completion_times[flow_id] = completion_time
                        
                        # 增量更新竞争计数
                        self._remove_flow_from_contention(completed_flow)
                        
                        # print(f"时间 {completion_time:.3f}s: 流 {flow_id} 完成传输。")
                        pbar.update(1)

            # 5. 推进时间
            self.current_time += self.timestep

            # 检查是否所有流都已完成
            if not self.active_flows and not pending_times:
                print(f"所有流已完成，仿真在时间 {self.current_time:.3f}s 结束。")
                break
        
        pbar.close()
        self.calculate_statistics()
        return self.get_simulation_results()

    def get_simulation_results(self):
        """
        返回仿真结果，主要为每个流的完成时间(FCT)和总延迟。
        """
        results = {}
        for flow_id, flow_info in self.completed_flows.items():
            fct = flow_info['end_time'] - flow_info['start_time']
            results[flow_id] = {
                "fct": fct,
                "start_time": flow_info['start_time'],
                "end_time": flow_info['end_time'],
                "size_mb": flow_info['size']
            }
        
        # 处理未完成的流 (如果仿真时间耗尽)
        for flow_id, flow_info in self.active_flows.items():
             results[flow_id] = {
                "fct": -1, # -1 表示未完成
                "start_time": flow_info['start_time'],
                "end_time": -1,
                "size_mb": flow_info['size']
            }
        return results

    def calculate_statistics(self):
        """计算最终的统计信息"""
        total_flows = len(self.tasks_data)
        completed_count = len(self.completed_flows)
        
        print(f"\n=== 仿真统计 (最终时间: {self.current_time:.3f}s) ===")
        print(f"总流数: {total_flows}")
        print(f"已完成: {completed_count}")
        print(f"未完成: {total_flows - completed_count}")
        
        if completed_count > 0:
            completion_rate = completed_count / total_flows
            fcts = [res['fct'] for res in self.get_simulation_results().values() if res['fct'] != -1]
            if fcts:
                avg_fct = np.mean(fcts)
                print(f"完成率: {completion_rate:.2%}")
                print(f"平均流完成时间 (FCT): {avg_fct:.4f}s")
        else:
            print("没有流完成。")





class FlowDataGenerator:
    """流数据生成器 (策略执行者) + 时间聚集支持"""
    
    def __init__(self, clos_topology, traffic_patterns_config):
        self.clos = clos_topology
        self.path_manager = PathManager(self.clos)
        self.config = traffic_patterns_config

        # 创建所有可用的策略实例
        self.strategies = {
            "normal": NormalTraffic(self.config['patterns']['normal']),
            "burst": BurstTraffic(self.config['patterns']['burst'])
        }
        
        # 创建时间聚集管理器
        self.cluster_manager = TimeClusterManager(self.config.get('time_clustering', {}))

    def generate_flow_batch(self, batch_id, flow_counter_start=0):
        """根据配置选择一个策略来生成批次，并应用时间聚集"""
        
        # 决定使用哪个策略
        if random.random() < self.config['burst_ratio']:
            strategy = self.strategies['burst']
            print("  -> 选择 'BurstTraffic' 策略")
        else:
            strategy = self.strategies['normal']
            print("  -> 选择 'NormalTraffic' 策略")
            
        # 执行策略生成流
        batch_result = strategy.generate_flows(batch_id, self.path_manager, flow_counter_start)
        
        # 应用时间聚集
        if batch_result and batch_result.get("flows"):
            print("  -> 应用时间聚集处理")
            clustered_flows = self.cluster_manager.apply_time_clustering(
                batch_result["flows"], self.path_manager
            )
            batch_result["flows"] = clustered_flows
            
            # 更新元数据中的聚集信息
            clustered_count = len([f for f in clustered_flows if f.get('is_clustered', False)])
            batch_result["metadata"]["clustered_flows"] = clustered_count
            batch_result["metadata"]["clustering_ratio"] = clustered_count / len(clustered_flows) if clustered_flows else 0
        
        return batch_result


def generate_unified_batches(total_batches, output_dir, params):
    """统一生成所有批次数据 - 确保IID合规性
    
    Args:
        total_batches (int): 总批次数量
        output_dir (str): 临时输出目录
        params (dict): 生成参数
        
    Returns:
        list: 成功生成的批次文件名列表
    """
    # 创建临时目录
    temp_dir = f"{output_dir}/all_batches"
    os.makedirs(temp_dir, exist_ok=True)
    
    generated_files = []
    global_flow_counter = 0  # 全局唯一的flow_counter
    
    print(f"开始统一生成 {total_batches} 个批次...")
    
    for batch_idx in range(total_batches):
        print(f"生成批次 {batch_idx + 1}/{total_batches}")
        
        # 创建独立的拓扑
        clos = Clos(12, 16, 4, ps_bandwidth=4000, sh_bandwidth=4000)
        clos.build()
        
        # 创建数据生成器 - 使用策略模式 + 时间聚集
        traffic_config = params["traffic_patterns"].copy()
        traffic_config["time_clustering"] = params.get("time_clustering", {})
        generator = FlowDataGenerator(clos, traffic_config)
        
        # 生成批次 - 传递当前的flow_counter起始值
        batch = generator.generate_flow_batch(f"unified_batch{batch_idx}", global_flow_counter)
        
        # 更新全局flow_counter
        global_flow_counter = batch.get("next_flow_counter", global_flow_counter)
        
        if not batch["flows"]:
            print(f"警告: 批次 {batch_idx} 没有成功生成任何流，跳过")
            continue
        
        # 运行仿真获取延迟数据
        batch_data = _simulate_single_batch(
            clos, batch, batch_idx, "unified"
        )
        
        if batch_data:
            # 保存到临时目录
            filename = f"batch_{batch_idx}.json"
            filepath = f"{temp_dir}/{filename}"
            
            with open(filepath, 'w') as f:
                json.dump(batch_data, f, indent=4)
            
            generated_files.append(filename)
            
            # 输出批次统计
            burst_info = " (爆发模式)" if batch.get("metadata", {}).get("is_burst", False) else ""
            clustered_flows = batch.get("metadata", {}).get("clustered_flows", 0)
            if clustered_flows > 0:
                print(f"批次 {batch_idx} 完成{burst_info}，生成 {len(batch['flows'])} 个流 (其中 {clustered_flows} 个聚集流)")
            else:
                print(f"批次 {batch_idx} 完成{burst_info}，生成 {len(batch['flows'])} 个流")
    
    print(f"统一生成完成，成功生成 {len(generated_files)} 个批次")
    return generated_files


def random_split_batches(batch_files, num_train, num_validation, num_test):
    """随机划分批次到训练集、验证集、测试集
    
    Args:
        batch_files (list): 批次文件名列表
        num_train (int): 训练集数量
        num_validation (int): 验证集数量
        num_test (int): 测试集数量
        
    Returns:
        dict: 划分后的文件列表字典
    """
    if len(batch_files) < num_train + num_validation + num_test:
        raise ValueError(f"批次文件数量不足: 需要 {num_train + num_validation + num_test}，实际 {len(batch_files)}")
    
    # 随机打乱批次文件列表 - 这是关键步骤
    shuffled_files = batch_files.copy()
    random.shuffle(shuffled_files)
    
    # 按比例切分
    train_files = shuffled_files[:num_train]
    validation_files = shuffled_files[num_train:num_train + num_validation]
    test_files = shuffled_files[num_train + num_validation:num_train + num_validation + num_test]
    
    print(f"随机划分完成:")
    print(f"  训练集: {len(train_files)} 个批次")
    print(f"  验证集: {len(validation_files)} 个批次")
    print(f"  测试集: {len(test_files)} 个批次")
    
    return {
        "train": train_files,
        "validation": validation_files,
        "test": test_files
    }


def move_batches_to_splits(temp_dir, splits_dict, output_base_dir):
    """将批次文件从临时目录移动到最终目录结构
    
    Args:
        temp_dir (str): 临时目录路径
        splits_dict (dict): 划分后的文件列表字典
        output_base_dir (str): 最终输出基础目录
        
    Returns:
        dict: 移动后的文件统计
    """
    import shutil
    
    # 创建最终目录结构
    for split_name in splits_dict.keys():
        os.makedirs(f"{output_base_dir}/{split_name}", exist_ok=True)
    
    moved_stats = {}
    
    for split_name, file_list in splits_dict.items():
        moved_count = 0
        
        for new_idx, filename in enumerate(file_list):
            # 源文件路径
            src_path = f"{temp_dir}/{filename}"
            
            # 目标文件路径 - 重新编号
            dst_path = f"{output_base_dir}/{split_name}/batch_{new_idx}.json"
            
            if os.path.exists(src_path):
                shutil.move(src_path, dst_path)
                moved_count += 1
            else:
                print(f"警告: 源文件不存在: {src_path}")
        
        moved_stats[split_name] = moved_count
        print(f"移动 {split_name} 数据集: {moved_count} 个批次")
    
    return moved_stats


def validate_iid_compliance(output_base_dir, splits_dict):
    """验证IID合规性 - 检查三个数据集的分布一致性
    
    Args:
        output_base_dir (str): 输出基础目录
        splits_dict (dict): 划分后的文件列表字典
        
    Returns:
        dict: 验证结果统计
    """
    validation_results = {
        "splits_stats": {},
        "burst_ratios": {},
        "iid_compliance": True,
        "distribution_variance": 0.0
    }
    
    print("\n=== IID合规性验证 ===")
    
    for split_name, file_list in splits_dict.items():
        split_dir = f"{output_base_dir}/{split_name}"
        
        total_batches = 0
        burst_batches = 0
        total_flows = 0
        clustered_flows = 0
        total_cluster_points = 0
        
        for batch_idx in range(len(file_list)):
            batch_file = f"{split_dir}/batch_{batch_idx}.json"
            
            if os.path.exists(batch_file):
                with open(batch_file, 'r') as f:
                    batch_data = json.load(f)
                
                total_batches += 1
                total_flows += len(batch_data.get("flows", []))
                
                # 检查是否为爆发批次
                if batch_data.get("_burst_info", {}).get("is_burst", False):
                    burst_batches += 1
                
                # 统计聚集信息
                batch_clustered = batch_data.get("_burst_info", {}).get("clustered_flows", 0)
                if batch_clustered == 0:
                    # 如果_burst_info中没有，检查直接的流数据
                    for flow in batch_data.get("flows", []):
                        if flow.get("inputs", {}).get("is_clustered", False):
                            batch_clustered += 1
                
                clustered_flows += batch_clustered
                
                # 统计聚集点数量（通过检查不同的cluster_time）
                cluster_times = set()
                for flow in batch_data.get("flows", []):
                    cluster_time = flow.get("inputs", {}).get("cluster_time")
                    if cluster_time is not None:
                        cluster_times.add(cluster_time)
                total_cluster_points += len(cluster_times)
        
        burst_ratio = burst_batches / total_batches if total_batches > 0 else 0
        
        validation_results["splits_stats"][split_name] = {
            "total_batches": total_batches,
            "burst_batches": burst_batches,
            "total_flows": total_flows,
            "clustered_flows": clustered_flows,
            "total_cluster_points": total_cluster_points,
            "burst_ratio": burst_ratio,
            "clustering_ratio": clustered_flows / total_flows if total_flows > 0 else 0
        }
        
        validation_results["burst_ratios"][split_name] = burst_ratio
        
        print(f"{split_name.upper()} 数据集:")
        print(f"  总批次数: {total_batches}")
        print(f"  爆发批次数: {burst_batches}")
        print(f"  爆发批次比例: {burst_ratio:.2%}")
        print(f"  总流数: {total_flows}")
        print(f"  聚集流数: {clustered_flows}")
        print(f"  聚集点数: {total_cluster_points}")
        print(f"  聚集比例: {clustered_flows / total_flows * 100 if total_flows > 0 else 0:.1f}%")
    
    # 计算分布方差
    burst_ratios = list(validation_results["burst_ratios"].values())
    if len(burst_ratios) > 1:
        mean_ratio = np.mean(burst_ratios)
        variance = np.var(burst_ratios)
        validation_results["distribution_variance"] = variance
        
        print(f"\n分布一致性分析:")
        print(f"  平均爆发比例: {mean_ratio:.2%}")
        print(f"  方差: {variance:.6f}")
        
        # 判断IID合规性 - 方差应该很小
        if variance > 0.01:  # 1%的方差阈值
            validation_results["iid_compliance"] = False
            print(f"  ⚠️  IID合规性检查失败: 方差过大 ({variance:.6f} > 0.01)")
        else:
            print(f"  ✅ IID合规性检查通过: 分布一致")
    
    return validation_results


def _simulate_single_batch(clos, batch, batch_idx, dataset_name):
    """仿真单个批次并获取延迟数据 - 适配流水线调度器"""
    try:
        # 准备所有流的任务数据
        all_tasks = []
        
        for flow in batch["flows"]:
            task = {
                "task_id": flow["flow_id"],
                "source": flow["source"],
                "target": flow["target"],
                "gradient_size_mb": flow["flow_features"][0],
                "comm_start_time": flow["start_time"],
                "paths": [flow["path"]]
            }
            all_tasks.append(task)
        
        if not all_tasks:
            return None

        # 创建新的流水线调度器
        scheduler = TimeBasedFlowScheduler(
            clos,
            tasks_data=all_tasks,
            timestep=0.001,  # 使用更小的时间步以提高精度
            max_time=500.0  # 增加额外时间确保所有流完成
        )
        
        # 运行仿真并获取结果
        simulation_results = scheduler.simulate_time_based()
        
        # 构建批次数据
        batch_data = {
            "batch_id": batch["batch_id"],
            "flows": []
        }
        
        # 保存爆发模式信息到全局统计中
        if batch.get("metadata", {}).get("is_burst", False):
            batch_data["_burst_info"] = batch["metadata"]
            
        # 处理每个流的数据
        for flow in batch["flows"]:
            flow_id = flow["flow_id"]
            
            # 获取该流的仿真结果
            result = simulation_results.get(flow_id, {"fct": -1}) # 如果找不到则标记为-1
            
            # 构建流数据 - 目标变量现在是FCT，包含聚集信息，不包含is_congested
            flow_data = {
                "inputs": {
                    "flow_id": flow_id,
                    "flow_features": flow["flow_features"],
                    "path": flow["path"],
                    "start_time": flow["start_time"],
                    "model": flow["workload"]["model"],
                    "dataset": flow["workload"]["dataset"],
                    "parameters": flow["workload"]["parameters"],
                    "is_clustered": flow.get("is_clustered", False),
                    "cluster_time": flow.get("cluster_time", None)
                },
                "time_delay": result["fct"]  # 这里的time_delay现在是总的FCT
            }
            
            # 只保留成功完成的流
            if result["fct"] > 0:
                batch_data["flows"].append(flow_data)

        # 只有在批次中至少有一个成功完成的流时才返回数据
        if not batch_data["flows"]:
            print(f"警告: 批次 {batch_idx} 没有成功完成任何流，将跳过此批次。")
            return None

        return batch_data
        
    except Exception as e:
        print(f"仿真批次 {batch_idx} 时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def generate_flow_data(config_params=None):
    """生成异步流数据集 - IID合规版本
    
    这个版本确保训练集、验证集、测试集来自同一分布，符合机器学习的IID假设。
    流程：统一生成所有批次 -> 随机打乱 -> 按比例划分 -> 移动到最终目录
    
    Args:
        config_params (dict, optional): 自定义配置参数，如果为None则使用全局配置
                                       可以传入部分参数来覆盖默认配置
    
    Returns:
        dict: 包含验证结果和统计信息的字典
    """
    
    # 使用配置参数
    if config_params is None:
        params = config.params.copy()
    else:
        params = config.params.copy()
        params.update(config_params)
    
    # 打印配置信息
    print("使用以下配置生成IID合规的异步流数据集:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    print()
    
    # 提取参数
    num_batches = params["num_batches"]
    num_validation_batches = params["num_validation_batches"]
    num_test_batches = params["num_test_batches"]
    output_dir = "dataset_async"  # 临时目录名，固定值
    output_base_dir = params["output_base_dir"]
    ensure_iid = params.get("ensure_iid", True)
    
    if not ensure_iid:
        print("警告: ensure_iid=False不再支持，强制使用IID合规流程")
        ensure_iid = True
    
    # 计算总批次数
    total_batches = num_batches + num_validation_batches + num_test_batches
    
    print(f"开始IID合规的数据生成流程:")
    print(f"  总批次数: {total_batches}")
    print(f"  训练集: {num_batches} 批次")
    print(f"  验证集: {num_validation_batches} 批次")
    print(f"  测试集: {num_test_batches} 批次")
    print(f"  临时目录: {output_dir}")
    print(f"  最终目录: {output_base_dir}")
    print()
    
    try:
        # 步骤1: 统一生成所有批次
        print("=== 步骤 1/5: 统一生成所有批次 ===")
        generated_files = generate_unified_batches(total_batches, output_dir, params)
        
        if len(generated_files) < total_batches:
            print(f"警告: 期望生成 {total_batches} 个批次，实际生成 {len(generated_files)} 个")
        
        # 步骤2: 随机划分批次
        print("\n=== 步骤 2/5: 随机划分批次 ===")
        splits_dict = random_split_batches(
            generated_files, 
            num_batches, 
            num_validation_batches, 
            num_test_batches
        )
        
        # 步骤3: 移动文件到最终目录
        print(f"\n=== 步骤 3/5: 移动文件到最终目录 ===")
        temp_dir = f"{output_dir}/all_batches"
        moved_stats = move_batches_to_splits(temp_dir, splits_dict, output_base_dir)
        
        # 步骤4: 验证IID合规性
        print(f"\n=== 步骤 4/5: 验证IID合规性 ===")
        validation_results = validate_iid_compliance(output_base_dir, splits_dict)
        
        # 步骤5: 清理临时文件
        print(f"\n=== 步骤 5/5: 清理临时文件 ===")
        import shutil
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print(f"已删除临时目录: {temp_dir}")
        
        # 生成最终报告
        _generate_iid_compliance_report(output_base_dir, validation_results, params)
        
        # 输出最终统计
        print(f"\n=== IID合规数据集生成完成 ===")
        for split_name, stats in validation_results["splits_stats"].items():
            print(f"{split_name.upper()}: {stats['total_batches']} 批次, {stats['total_flows']} 流, 爆发比例 {stats['burst_ratio']:.1%}")
        
        if validation_results["iid_compliance"]:
            print("✅ IID合规性验证通过")
        else:
            print("⚠️ IID合规性验证失败")
        
        return {
            "validation_results": validation_results,
            "moved_stats": moved_stats,
            "splits_dict": splits_dict,
            "output_base_dir": output_base_dir
        }
        
    except Exception as e:
        print(f"数据生成过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None





def _generate_iid_compliance_report(output_base_dir, validation_results, params):
    """生成IID合规性报告"""
    
    doc_content = """# IID合规性数据生成报告

## 概述
此文档记录了数据集的IID (Independent and Identically Distributed) 合规性验证结果。

## IID合规性的重要性
机器学习的核心假设是训练集、验证集和测试集应该来自同一分布。只有这样：
- 验证集才能有效指导训练过程
- 测试集才能真实反映模型在未知数据上的泛化能力
- 超参数调优才能产生可靠的结果

## 数据生成流程

### 统一生成策略
1. **统一批次生成**: 在单一循环中生成所有批次，确保全局随机性
2. **随机打乱**: 使用 `random.shuffle()` 打乱所有批次
3. **按比例划分**: 将打乱后的批次按固定比例分配到三个数据集
4. **文件重组**: 移动到最终目录结构并重新编号

### 配置参数
"""
    
    # 添加关键配置参数
    key_params = {
        "num_batches": params.get("num_batches", 0),
        "num_validation_batches": params.get("num_validation_batches", 0),
        "num_test_batches": params.get("num_test_batches", 0),
        "burst_mode_enabled": params.get("burst_mode_enabled", False),
        "burst_batch_ratio": params.get("burst_batch_ratio", 0),
        "flows_per_batch_range": params.get("flows_per_batch_range", (0, 0)),
        "congestion_ratio_range": params.get("congestion_ratio_range", (0, 0))
    }
    
    for key, value in key_params.items():
        doc_content += f"- **{key}**: {value}\n"
    
    doc_content += f"""
## 验证结果

### 整体统计
"""
    
    # 添加整体统计
    total_batches = sum(stats["total_batches"] for stats in validation_results["splits_stats"].values())
    total_flows = sum(stats["total_flows"] for stats in validation_results["splits_stats"].values())
    total_burst_batches = sum(stats["burst_batches"] for stats in validation_results["splits_stats"].values())
    
    doc_content += f"- **总批次数**: {total_batches}\n"
    doc_content += f"- **总流数**: {total_flows}\n"
    doc_content += f"- **总爆发批次数**: {total_burst_batches}\n"
    doc_content += f"- **平均爆发比例**: {total_burst_batches/total_batches*100:.1f}%\n"
    doc_content += f"- **分布方差**: {validation_results['distribution_variance']:.6f}\n"
    
    # IID合规性状态
    compliance_status = "✅ 通过" if validation_results["iid_compliance"] else "❌ 失败"
    doc_content += f"- **IID合规性**: {compliance_status}\n\n"
    
    # 详细的数据集统计
    doc_content += "### 各数据集详细统计\n\n"
    
    for split_name, stats in validation_results["splits_stats"].items():
        doc_content += f"#### {split_name.upper()} 数据集\n"
        doc_content += f"- 总批次数: {stats['total_batches']}\n"
        doc_content += f"- 爆发批次数: {stats['burst_batches']}\n"
        doc_content += f"- 爆发比例: {stats['burst_ratio']:.2%}\n"
        doc_content += f"- 总流数: {stats['total_flows']}\n"
        doc_content += f"- 聚集流数: {stats.get('clustered_flows', 0)}\n\n"
    
    # 分布一致性分析
    doc_content += "### 分布一致性分析\n\n"
    burst_ratios = validation_results["burst_ratios"]
    doc_content += "各数据集爆发比例对比:\n"
    for split_name, ratio in burst_ratios.items():
        doc_content += f"- {split_name}: {ratio:.2%}\n"
    
    doc_content += f"\n方差分析: {validation_results['distribution_variance']:.6f}\n"
    
    if validation_results["iid_compliance"]:
        doc_content += "\n✅ **结论**: 数据集满足IID假设，各数据集分布一致，可以安全用于机器学习训练和评估。\n"
    else:
        doc_content += "\n⚠️ **结论**: 数据集不满足IID假设，建议重新生成数据或调整参数。\n"
    
    doc_content += """
## 技术说明

### IID合规性判断标准
- **方差阈值**: 0.01 (各数据集爆发比例方差应小于1%)
- **随机性**: 使用全局随机种子确保可重现性
- **划分策略**: 先生成后划分，避免分别生成导致的分布差异

### 文件结构
```
datasets/
├── train/
│   ├── batch_0.json
│   ├── batch_1.json
│   └── ...
├── validation/
│   ├── batch_0.json
│   ├── batch_1.json
│   └── ...
└── test/
    ├── batch_0.json
    ├── batch_1.json
    └── ...
```

---
*此报告由IID合规性数据生成器自动生成*
"""
    
    # 保存报告
    with open(f'{output_base_dir}/iid_compliance_report.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print(f"IID合规性报告已保存到: {output_base_dir}/iid_compliance_report.md")

def parse_arguments():
    """解析命令行参数，支持异步流数据生成配置"""
    # 初始化一个全局配置实例，用于获取默认值
    default_config = DataGenConfig().params
    default_patterns = default_config['traffic_patterns']
    default_normal = default_patterns['patterns']['normal']
    default_burst = default_patterns['patterns']['burst']

    parser = argparse.ArgumentParser(
        description='网络延迟预测数据集生成器 (流水线模式)',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # --- 全局参数组 ---
    g_group = parser.add_argument_group('全局生成参数')
    g_group.add_argument('--num-batches', type=int, default=default_config['num_batches'],
                         help='训练批次数量')
    g_group.add_argument('--num-validation-batches', type=int, default=default_config['num_validation_batches'],
                         help='验证批次数量')
    g_group.add_argument('--num-test-batches', type=int, default=default_config['num_test_batches'],
                         help='测试批次数量')
    g_group.add_argument('--output-base-dir', type=str, default=default_config['output_base_dir'],
                         help='最终输出数据集的基础目录')
    g_group.add_argument('--ensure-iid', type=bool, default=default_config['ensure_iid'],
                         help='是否启用IID合规的数据划分流程')

    # --- 流量模式通用参数组 ---
    p_group = parser.add_argument_group('流量模式通用参数')
    p_group.add_argument('--burst-ratio', type=float, default=default_patterns['burst_ratio'],
                         help='爆发模式批次在总批次中占的比例')
    
    # --- 时间聚集参数组 ---
    c_group = parser.add_argument_group('时间聚集参数')
    default_clustering = default_config.get('time_clustering', {})
    c_group.add_argument('--clustering-enabled', type=bool, default=default_clustering.get('enabled', True),
                         help='是否启用时间聚集功能')
    c_group.add_argument('--clustering-ratio', type=float, default=default_clustering.get('clustering_ratio', 0.3),
                         help='参与聚集的流比例 (0.0-1.0)')
    c_group.add_argument('--flows-per-cluster-min', type=int, default=default_clustering.get('flows_per_cluster_range', (2,3))[0],
                         help='每个聚集点的最小流数量')
    c_group.add_argument('--flows-per-cluster-max', type=int, default=default_clustering.get('flows_per_cluster_range', (2,3))[1],
                         help='每个聚集点的最大流数量')
    c_group.add_argument('--min-cluster-interval', type=float, default=default_clustering.get('min_cluster_interval', 0.1),
                         help='聚集点之间的最小时间间隔(秒)')
    c_group.add_argument('--path-diversity-check', type=bool, default=default_clustering.get('path_diversity_check', True),
                         help='是否启用路径多样性检查')

    # --- 普通流量模式参数组 ---
    n_group = parser.add_argument_group('普通流量模式参数')
    n_group.add_argument('--normal-flows-min', type=int, default=default_normal['flows_per_batch_range'][0],
                         help='[普通模式] 每批流数量范围的最小值')
    n_group.add_argument('--normal-flows-max', type=int, default=default_normal['flows_per_batch_range'][1],
                         help='[普通模式] 每批流数量范围的最大值')
    n_group.add_argument('--normal-arrival-min', type=float, default=default_normal['flow_arrival_interval_range'][0],
                         help='[普通模式] 流到达间隔范围的最小值(秒)')
    n_group.add_argument('--normal-arrival-max', type=float, default=default_normal['flow_arrival_interval_range'][1],
                         help='[普通模式] 流到达间隔范围的最大值(秒)')
    n_group.add_argument('--normal-size-min', type=float, default=default_normal['flow_size_range'][0],
                         help='[普通模式] 流大小范围的最小值(MB)')
    n_group.add_argument('--normal-size-max', type=float, default=default_normal['flow_size_range'][1],
                         help='[普通模式] 流大小范围的最大值(MB)')
    n_group.add_argument('--normal-congestion-min', type=float, default=default_normal['congestion_ratio_range'][0],
                         help='[普通模式] 拥塞比例范围的最小值')
    n_group.add_argument('--normal-congestion-max', type=float, default=default_normal['congestion_ratio_range'][1],
                         help='[普通模式] 拥塞比例范围的最大值')
    n_group.add_argument('--normal-target-pods', type=int, default=default_normal['target_pods_count'],
                         help='[普通模式] 目标Pod数量（用于制造拥塞）')

    # --- 爆发流量模式参数组 ---
    b_group = parser.add_argument_group('爆发流量模式参数')
    b_group.add_argument('--burst-flow-min', type=int, default=default_burst['flow_count_range'][0],
                         help='[爆发模式] 每批流数量范围的最小值')
    b_group.add_argument('--burst-flow-max', type=int, default=default_burst['flow_count_range'][1],
                         help='[爆发模式] 每批流数量范围的最大值')
    b_group.add_argument('--burst-time-window-min', type=float, default=default_burst['time_window'][0],
                         help='[爆发模式] 单次爆发时间窗口的最小值(秒)')
    b_group.add_argument('--burst-time-window-max', type=float, default=default_burst['time_window'][1],
                         help='[爆发模式] 单次爆发时间窗口的最大值(秒)')
    b_group.add_argument('--burst-waves-min', type=int, default=default_burst['waves_count_range'][0],
                         help='[爆发模式] 波次数量范围的最小值')
    b_group.add_argument('--burst-waves-max', type=int, default=default_burst['waves_count_range'][1],
                         help='[爆发模式] 波次数量范围的最大值')
    b_group.add_argument('--burst-wave-interval-min', type=float, default=default_burst['wave_interval_range'][0],
                         help='[爆发模式] 波次间隔范围的最小值(秒)')
    b_group.add_argument('--burst-wave-interval-max', type=float, default=default_burst['wave_interval_range'][1],
                         help='[爆发模式] 波次间隔范围的最大值(秒)')
    b_group.add_argument('--burst-intensity-decay', type=float, default=default_burst['intensity_decay'],
                         help='[爆发模式] 后续波次强度衰减因子')
    b_group.add_argument('--burst-target-pods', type=int, default=default_burst['target_pods_count'],
                         help='[爆发模式] 目标Pod数量（用于制造拥塞）')
    b_group.add_argument('--burst-congestion-ratio', type=float, default=default_burst['congestion_flow_ratio'],
                         help='[爆发模式] 指向目标Pod的流比例')
    b_group.add_argument('--burst-large-flow-ratio', type=float, default=default_burst['large_flow_ratio'],
                         help='[爆发模式] 大流的比例 (300-500MB)')
    b_group.add_argument('--burst-medium-flow-ratio', type=float, default=default_burst['medium_flow_ratio'],
                         help='[爆发模式] 中等流的比例 (100-300MB)')
    b_group.add_argument('--burst-small-flow-ratio', type=float, default=default_burst['small_flow_ratio'],
                         help='[爆发模式] 小流的比例 (50-100MB)')
    
    return parser.parse_args()

def args_to_params(args):
    """将命令行参数转换为配置参数"""
    return {
        # 全局配置
        "num_batches": args.num_batches,
        "num_validation_batches": args.num_validation_batches,
        "num_test_batches": args.num_test_batches,
        "output_base_dir": args.output_base_dir,
        "ensure_iid": args.ensure_iid,
        
        # 时间聚集配置
        "time_clustering": {
            "enabled": args.clustering_enabled,
            "clustering_ratio": args.clustering_ratio,
            "flows_per_cluster_range": (args.flows_per_cluster_min, args.flows_per_cluster_max),
            "min_cluster_interval": args.min_cluster_interval,
            "path_diversity_check": args.path_diversity_check,
        },
        
        # 流量模式配置
        "traffic_patterns": {
            "burst_ratio": args.burst_ratio,
            "patterns": {
                "normal": {
                    "flows_per_batch_range": (args.normal_flows_min, args.normal_flows_max),
                    "flow_arrival_interval_range": (args.normal_arrival_min, args.normal_arrival_max),
                    "flow_size_range": (args.normal_size_min, args.normal_size_max),
                    "congestion_ratio_range": (args.normal_congestion_min, args.normal_congestion_max),
                    "target_pods_count": args.normal_target_pods,
                },
                "burst": {
                    "flow_count_range": (args.burst_flow_min, args.burst_flow_max),
                    "time_window": (args.burst_time_window_min, args.burst_time_window_max),
                    "waves_count_range": (args.burst_waves_min, args.burst_waves_max),
                    "wave_interval_range": (args.burst_wave_interval_min, args.burst_wave_interval_max),
                    "intensity_decay": args.burst_intensity_decay,
                    "target_pods_count": args.burst_target_pods,
                    "congestion_flow_ratio": args.burst_congestion_ratio,
                    "large_flow_ratio": args.burst_large_flow_ratio,
                    "medium_flow_ratio": args.burst_medium_flow_ratio,
                    "small_flow_ratio": args.burst_small_flow_ratio,
                }
            }
        }
    }

# 主函数 - 支持argparse参数解析
if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()
    
    print("流水线模式数据生成器启动")
    print("=" * 50)
    
    # 将命令行参数转换为配置字典
    params = args_to_params(args)
    
    # 打印配置信息
    print("\n使用以下配置:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    print()
    
    # 生成流数据
    print("生成流数据集...")
    generate_flow_data(params)
    
    print("\n数据生成完成！")